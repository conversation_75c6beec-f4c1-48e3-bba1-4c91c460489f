<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');

// Add the modern CSS files
echo '<link rel="stylesheet" href="/choims/assets/css/inventory-additions-modern.css">';
echo '<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">';

// Ensure user has appropriate role
requireLogin();
requireRole('GodMode', 'Superadmin', 'Logistics');

// Get filter parameters
$time_period = isset($_GET['time_period']) ? sanitizeInput($_GET['time_period']) : 'month';
$location_id = isset($_GET['location_id']) ? sanitizeInput($_GET['location_id']) : '';
$category_id = isset($_GET['category_id']) ? sanitizeInput($_GET['category_id']) : '';
$report_type = isset($_GET['type']) ? sanitizeInput($_GET['type']) : 'consumable';

// Set date range based on selected time period
if ($time_period === 'day') {
    $start_date = date('Y-m-d');
    $end_date = date('Y-m-d');
    $period_label = 'Today';
} elseif ($time_period === 'week') {
    $start_date = date('Y-m-d', strtotime('monday this week'));
    $end_date = date('Y-m-d', strtotime('sunday this week'));
    $period_label = 'This Week';
} elseif ($time_period === 'month') {
    $start_date = date('Y-m-01'); // First day of current month
    $end_date = date('Y-m-t'); // Last day of current month
    $period_label = 'This Month';
} elseif ($time_period === 'specific_date' && isset($_GET['specific_date'])) {
    // Use specific date for both start and end date
    $start_date = sanitizeInput($_GET['specific_date']);
    $end_date = sanitizeInput($_GET['specific_date']);
    $period_label = 'Specific Date (' . date('M d, Y', strtotime($start_date)) . ')';
} elseif ($time_period === 'custom' && isset($_GET['custom_start_date']) && isset($_GET['custom_end_date'])) {
    // Only use custom dates if time_period is explicitly set to 'custom'
    $start_date = sanitizeInput($_GET['custom_start_date']);
    $end_date = sanitizeInput($_GET['custom_end_date']);
    $period_label = 'Custom Range';
} else {
    // Default to month if invalid time period
    $time_period = 'month';
    $start_date = date('Y-m-01');
    $end_date = date('Y-m-t');
    $period_label = 'This Month';
}

// Get locations for dropdown
$locationsQuery = "SELECT location_id, location_name FROM locations ORDER BY location_name";
$locationsResult = mysqli_query($conn, $locationsQuery);

// Get categories based on report type
if ($report_type == 'consumable') {
    $categoriesQuery = "SELECT category_id, category_name FROM categories WHERE category_id IN (4, 5, 6) ORDER BY category_name";
} else {
    $categoriesQuery = "SELECT category_id, category_name FROM categories WHERE category_id IN (1, 2, 3) ORDER BY category_name";
}
$categoriesResult = mysqli_query($conn, $categoriesQuery);

// Initialize arrays and statistics variables
$additions = [];
$total_additions = 0;
$total_items = 0;
$total_value = 0;
$items_by_category = [];

// Build the query with filters based on report type
if ($report_type == 'consumable') {
    // Query for consumable inventory
    $query = "
        SELECT
            ct.transaction_id,
            ct.transaction_date,
            ct.transaction_type,
            ct.quantity,
            ct.reference_document,
            ct.unit_cost,
            ct.remarks,
            ci.inventory_id,
            sm.sku_code,
            sm.sku_name,
            c.category_name,
            c.category_id,
            l.location_name,
            l.location_id,
            u.full_name AS performed_by_name,
            s.supplier_name
        FROM consumable_transactions ct
        JOIN consumable_inventory ci ON ct.inventory_id = ci.inventory_id
        JOIN sku_master sm ON ci.sku_id = sm.sku_id
        JOIN categories c ON sm.category_id = c.category_id
        JOIN locations l ON ci.location_id = l.location_id
        LEFT JOIN users u ON ct.performed_by = u.user_id
        LEFT JOIN suppliers s ON ct.supplier_id = s.supplier_id
        WHERE ct.transaction_type = 'Stock In'
        AND DATE(ct.transaction_date) BETWEEN ? AND ?
    ";

    // Add location filter if provided
    if (!empty($location_id)) {
        $query .= " AND ci.location_id = " . intval($location_id);
    }

    // Add category filter if provided
    if (!empty($category_id)) {
        $query .= " AND c.category_id = " . intval($category_id);
    }

    // Add order by clause
    $query .= " ORDER BY ct.transaction_date DESC";

    // Execute query
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'ss', $start_date, $end_date);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    // Process results
    if (mysqli_num_rows($result) > 0) {
        while ($row = mysqli_fetch_assoc($result)) {
            $additions[] = $row;

            // Calculate statistics
            $total_additions++;
            $total_items += $row['quantity'];

            if (!empty($row['unit_cost'])) {
                $total_value += ($row['quantity'] * $row['unit_cost']);
            }

            // Count by category
            $cat_id = $row['category_id'];
            $cat_name = $row['category_name'];

            if (!isset($items_by_category[$cat_id])) {
                $items_by_category[$cat_id] = [
                    'name' => $cat_name,
                    'count' => 0,
                    'quantity' => 0
                ];
            }

            $items_by_category[$cat_id]['count']++;
            $items_by_category[$cat_id]['quantity'] += $row['quantity'];
        }
    }
} else {
    // Query for fixed assets
    $query = "
        SELECT
            fa.asset_id,
            fa.created_at as addition_date,
            fa.asset_name,
            fa.serial_number,
            fa.model,
            fa.specifications,
            fa.unit_cost,
            fa.local_mr as reference_document,
            fa.remarks,
            sm.sku_code,
            sm.sku_name,
            c.category_name,
            c.category_id,
            l.location_name,
            l.location_id,
            u.full_name AS created_by_name,
            s.supplier_name
        FROM fixed_assets fa
        JOIN sku_master sm ON fa.sku_id = sm.sku_id
        JOIN categories c ON sm.category_id = c.category_id
        JOIN locations l ON fa.current_location_id = l.location_id
        LEFT JOIN users u ON fa.created_by = u.user_id
        LEFT JOIN suppliers s ON fa.supplier_id = s.supplier_id
        WHERE DATE(fa.created_at) BETWEEN ? AND ?
    ";

    // Add location filter if provided
    if (!empty($location_id)) {
        $query .= " AND fa.current_location_id = " . intval($location_id);
    }

    // Add category filter if provided
    if (!empty($category_id)) {
        $query .= " AND c.category_id = " . intval($category_id);
    }

    // Add order by clause
    $query .= " ORDER BY fa.created_at DESC";

    // Execute query
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'ss', $start_date, $end_date);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    // Process results
    if (mysqli_num_rows($result) > 0) {
        while ($row = mysqli_fetch_assoc($result)) {
            // Map fields to match the structure of consumable inventory for consistent rendering
            $row['transaction_date'] = $row['addition_date'];
            $row['quantity'] = 1; // Fixed assets always have quantity 1

            $additions[] = $row;

            // Calculate statistics
            $total_additions++;
            $total_items++; // Each fixed asset counts as 1 item

            if (!empty($row['unit_cost'])) {
                $total_value += $row['unit_cost'];
            }

            // Count by category
            $cat_id = $row['category_id'];
            $cat_name = $row['category_name'];

            if (!isset($items_by_category[$cat_id])) {
                $items_by_category[$cat_id] = [
                    'name' => $cat_name,
                    'count' => 0,
                    'quantity' => 0
                ];
            }

            $items_by_category[$cat_id]['count']++;
            $items_by_category[$cat_id]['quantity']++;
        }
    }
}
?>

<div class="container-fluid">
    <!-- Modern page header -->
    <div class="page-header animate__animated animate__fadeIn" style="animation-duration: 0.05s !important;">
        <h1 class="page-title">
            <i class="fas fa-boxes"></i>
            Inventory Additions Report
        </h1>
        <div class="page-actions">
            <button id="exportExcelBtn" class="btn btn-outline animate__animated animate__fadeIn" style="animation-duration: 0.05s !important; animation-delay: 0.01s;">
                <i class="fas fa-file-excel me-2"></i> Export Excel
            </button>
            <button id="exportPdfBtn" class="btn btn-accent animate__animated animate__fadeIn" style="animation-duration: 0.05s !important; animation-delay: 0.02s;">
                <i class="fas fa-file-pdf me-2"></i> Export PDF
            </button>
            <button id="printReportBtn" class="btn btn-primary animate__animated animate__fadeIn" style="animation-duration: 0.05s !important; animation-delay: 0.03s;">
                <i class="fas fa-print me-2"></i> Print Report
            </button>
        </div>
    </div>

    <!-- Modern Report Type Tabs -->
    <div class="text-center animate__animated animate__fadeIn" style="animation-duration: 0.05s !important; animation-delay: 0.03s;">
        <ul class="nav nav-tabs">
            <li class="nav-item">
                <a class="nav-link <?php echo $report_type == 'consumable' ? 'active' : ''; ?>" href="?type=consumable<?php echo isset($_GET['time_period']) ? '&time_period=' . $_GET['time_period'] : ''; ?><?php echo $time_period === 'custom' ? '&custom_start_date=' . $start_date . '&custom_end_date=' . $end_date : ''; ?><?php echo $time_period === 'specific_date' ? '&specific_date=' . $start_date : ''; ?><?php echo isset($_GET['location_id']) ? '&location_id=' . $_GET['location_id'] : ''; ?><?php echo isset($_GET['category_id']) ? '&category_id=' . $_GET['category_id'] : ''; ?>">
                    <i class="fas fa-boxes me-2"></i>Consumable Inventory
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $report_type == 'fixed' ? 'active' : ''; ?>" href="?type=fixed<?php echo isset($_GET['time_period']) ? '&time_period=' . $_GET['time_period'] : ''; ?><?php echo $time_period === 'custom' ? '&custom_start_date=' . $start_date . '&custom_end_date=' . $end_date : ''; ?><?php echo $time_period === 'specific_date' ? '&specific_date=' . $start_date : ''; ?><?php echo isset($_GET['location_id']) ? '&location_id=' . $_GET['location_id'] : ''; ?><?php echo isset($_GET['category_id']) ? '&category_id=' . $_GET['category_id'] : ''; ?>">
                    <i class="fas fa-laptop me-2"></i>Fixed Assets
                </a>
            </li>
        </ul>
    </div>

    <!-- Modern Statistics Cards -->
    <div class="stats-container">
        <div class="stat-card stat-primary animate__animated animate__fadeInUp" style="animation-duration: 0.05s !important; animation-delay: 0.01s;">
            <div class="stat-icon">
                <i class="fas fa-boxes"></i>
            </div>
            <div class="stat-content">
                <div class="stat-value"><?php echo $total_additions; ?></div>
                <div class="stat-label">Total Additions</div>
            </div>
        </div>

        <div class="stat-card stat-success animate__animated animate__fadeInUp" style="animation-duration: 0.05s !important; animation-delay: 0.02s;">
            <div class="stat-icon">
                <i class="fas fa-cubes"></i>
            </div>
            <div class="stat-content">
                <div class="stat-value"><?php echo $total_items; ?></div>
                <div class="stat-label">Total Items Added</div>
            </div>
        </div>

        <div class="stat-card stat-info animate__animated animate__fadeInUp" style="animation-duration: 0.05s !important; animation-delay: 0.03s;">
            <div class="stat-icon">
                <i class="fas fa-money-bill-wave"></i>
            </div>
            <div class="stat-content">
                <div class="stat-value">₱<?php echo number_format($total_value, 2); ?></div>
                <div class="stat-label">Total Value</div>
            </div>
        </div>

        <div class="stat-card stat-warning animate__animated animate__fadeInUp" style="animation-duration: 0.05s !important; animation-delay: 0.04s;">
            <div class="stat-icon">
                <i class="fas fa-calendar-alt"></i>
            </div>
            <div class="stat-content">
                <div class="stat-value"><?php echo $period_label; ?></div>
                <div class="stat-label">Time Period</div>
            </div>
        </div>
    </div>

    <!-- Modern Filter Form -->
    <div class="card mb-4 animate__animated animate__fadeIn" style="animation-duration: 0.05s !important; animation-delay: 0.05s;">
        <div class="card-header">
            <button class="filter-toggle" type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapse" aria-expanded="true" aria-controls="filterCollapse">
                <h5 class="card-title mb-0"><i class="fas fa-sliders-h me-2"></i> Filter Report</h5>
                <i class="fas fa-chevron-down"></i>
            </button>
        </div>
        <div class="collapse show" id="filterCollapse">
            <div class="card-body">
                <form method="GET" action="" class="row g-4">
                    <!-- Hidden field for report type -->
                    <input type="hidden" name="type" value="<?php echo $report_type; ?>">

                    <!-- Time Period Selection -->
                    <div class="col-md-3">
                        <label for="time_period" class="form-label"><i class="far fa-clock"></i> Time Period</label>
                        <select class="form-select" id="time_period" name="time_period">
                            <option value="day" <?php echo $time_period === 'day' ? 'selected' : ''; ?>>Today</option>
                            <option value="week" <?php echo $time_period === 'week' ? 'selected' : ''; ?>>This Week</option>
                            <option value="month" <?php echo $time_period === 'month' ? 'selected' : ''; ?>>This Month</option>
                            <option value="specific_date" <?php echo $time_period === 'specific_date' ? 'selected' : ''; ?>>Specific Date</option>
                            <option value="custom" <?php echo $time_period === 'custom' ? 'selected' : ''; ?>>Custom Range</option>
                        </select>
                    </div>

                    <!-- Specific Date Selection -->
                    <div class="col-md-3" id="specificDateRange" style="display: <?php echo $time_period === 'specific_date' ? 'block' : 'none'; ?>">
                        <label for="specific_date" class="form-label"><i class="far fa-calendar-alt"></i> Select Date</label>
                        <input type="date" class="form-control" id="specific_date" name="specific_date" value="<?php echo $time_period === 'specific_date' ? $start_date : date('Y-m-d'); ?>">
                    </div>

                    <!-- Custom Date Range -->
                    <div class="col-md-6" id="customDateRange" style="display: <?php echo $time_period === 'custom' ? 'flex' : 'none'; ?>">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="start_date" class="form-label"><i class="far fa-calendar-alt"></i> Start Date</label>
                                <input type="date" class="form-control" id="start_date" name="custom_start_date" value="<?php echo $start_date; ?>">
                            </div>
                            <div class="col-md-6">
                                <label for="end_date" class="form-label"><i class="far fa-calendar-alt"></i> End Date</label>
                                <input type="date" class="form-control" id="end_date" name="custom_end_date" value="<?php echo $end_date; ?>">
                            </div>
                        </div>
                    </div>

                    <!-- Location Filter -->
                    <div class="col-md-3">
                        <label for="location_id" class="form-label"><i class="fas fa-map-marker-alt"></i> Location</label>
                        <select class="form-select" id="location_id" name="location_id">
                            <option value="">All Locations</option>
                            <?php mysqli_data_seek($locationsResult, 0); ?>
                            <?php while ($location = mysqli_fetch_assoc($locationsResult)): ?>
                                <option value="<?php echo $location['location_id']; ?>" <?php echo $location_id == $location['location_id'] ? 'selected' : ''; ?>>
                                    <?php echo $location['location_name']; ?>
                                </option>
                            <?php endwhile; ?>
                        </select>
                    </div>

                    <!-- Category Filter -->
                    <div class="col-md-3">
                        <label for="category_id" class="form-label"><i class="fas fa-tags"></i> Category</label>
                        <select class="form-select" id="category_id" name="category_id">
                            <option value="">All Categories</option>
                            <?php mysqli_data_seek($categoriesResult, 0); ?>
                            <?php while ($category = mysqli_fetch_assoc($categoriesResult)): ?>
                                <option value="<?php echo $category['category_id']; ?>" <?php echo $category_id == $category['category_id'] ? 'selected' : ''; ?>>
                                    <?php echo $category['category_name']; ?>
                                </option>
                            <?php endwhile; ?>
                        </select>
                    </div>

                    <!-- Filter Button -->
                    <div class="col-md-3 align-self-end">
                        <button type="submit" class="btn btn-accent w-100">
                            <i class="fas fa-filter me-2"></i> Apply Filters
                        </button>
                    </div>

                    <!-- Reset Button -->
                    <div class="col-md-3 align-self-end">
                        <a href="?type=<?php echo $report_type; ?>" class="btn btn-outline w-100">
                            <i class="fas fa-redo me-2"></i> Reset Filters
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modern Report Content -->
    <div class="card mb-4 animate__animated animate__fadeIn" style="animation-duration: 0.05s !important; animation-delay: 0.06s;">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-clipboard-list me-2"></i>
                <?php echo $report_type == 'consumable' ? 'Consumable Inventory' : 'Fixed Asset'; ?> Additions
                <span class="text-muted ms-2">(<?php echo date('M d, Y', strtotime($start_date)); ?> - <?php echo date('M d, Y', strtotime($end_date)); ?>)</span>
            </h5>
        </div>
        <div class="card-body" id="reportContent">
            <!-- Print-only header -->
            <div class="report-header d-none d-print-block">
                <table width="100%" style="margin-bottom: 20px;">
                    <tr>
                        <td width="20%" style="text-align: left; vertical-align: middle;">
                            <img src="/choims/assets/img/prqlogo1.jpg" alt="Logo" style="height: 80px; width: auto;">
                        </td>
                        <td width="60%" style="text-align: center; vertical-align: middle;">
                            <h2 style="margin-bottom: 5px; font-size: 20px; font-weight: bold;">
                                <?php echo $report_type == 'consumable' ? 'Consumable Inventory' : 'Fixed Asset'; ?> Additions Report
                            </h2>
                            <p style="margin: 0; font-size: 14px;">
                                <strong>Time Period:</strong> <?php echo date('M d, Y', strtotime($start_date)); ?> - <?php echo date('M d, Y', strtotime($end_date)); ?>
                            </p>
                        </td>
                        <td width="20%" style="text-align: right; vertical-align: middle;">
                            <p style="margin: 0; font-size: 12px;">
                                <strong>Printed by:</strong> <?php echo $_SESSION['full_name'] ?? $_SESSION['username']; ?><br>
                                <strong>Date & Time:</strong> <?php echo date('F d, Y h:i A'); ?>
                            </p>
                        </td>
                    </tr>
                </table>

                <div style="text-align: center; margin-bottom: 20px; border-top: 1px solid #ddd; border-bottom: 1px solid #ddd; padding: 10px 0;">
                    <p style="margin: 0; font-size: 13px;">
                        <strong>Total Items Added:</strong> <?php echo $total_items; ?> |
                        <strong>Total Value:</strong> ₱<?php echo number_format($total_value, 2); ?>
                        <?php if (!empty($location_id)): ?>
                         | <strong>Location:</strong> <?php
                            mysqli_data_seek($locationsResult, 0);
                            while ($location = mysqli_fetch_assoc($locationsResult)) {
                                if ($location['location_id'] == $location_id) {
                                    echo $location['location_name'];
                                    break;
                                }
                            }
                        ?>
                        <?php endif; ?>
                        <?php if (!empty($category_id)): ?>
                         | <strong>Category:</strong> <?php
                            mysqli_data_seek($categoriesResult, 0);
                            while ($category = mysqli_fetch_assoc($categoriesResult)) {
                                if ($category['category_id'] == $category_id) {
                                    echo $category['category_name'];
                                    break;
                                }
                            }
                        ?>
                        <?php endif; ?>
                    </p>
                </div>
            </div>
            <?php if (count($additions) > 0): ?>
                <!-- Data summary -->
                <div class="data-summary mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Showing <?php echo count($additions); ?> records</h6>
                        </div>
                        <div class="d-flex gap-2">
                            <div class="input-group">
                                <input type="text" id="tableSearch" class="form-control" placeholder="Search items...">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="table-container">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="additionsTable">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>SKU</th>
                                    <th>Item Name</th>
                                    <th>Category</th>
                                    <th>Location</th>
                                    <?php if ($report_type == 'fixed'): ?>
                                    <th>Serial Number</th>
                                    <?php else: ?>
                                    <th>Quantity</th>
                                    <?php endif; ?>
                                    <th>Unit Cost</th>
                                    <th>Total Value</th>
                                    <th>Reference</th>
                                    <th>Added By</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($additions as $addition): ?>
                                    <tr style="animation: fadeIn 0.05s ease forwards;">
                                        <td><?php echo date('M d, Y', strtotime($addition['transaction_date'])); ?></td>
                                        <td><span class="badge bg-light text-dark"><?php echo $addition['sku_code']; ?></span></td>
                                        <td>
                                            <strong>
                                            <?php
                                            if ($report_type == 'fixed' && !empty($addition['asset_name'])) {
                                                echo $addition['asset_name'];
                                            } else {
                                                echo $addition['sku_name'];
                                            }
                                            ?>
                                            </strong>
                                        </td>
                                        <td><span class="badge badge-info"><?php echo $addition['category_name']; ?></span></td>
                                        <td><?php echo $addition['location_name']; ?></td>
                                        <?php if ($report_type == 'fixed'): ?>
                                        <td><?php echo $addition['serial_number'] ?? '-'; ?></td>
                                        <?php else: ?>
                                        <td><span class="badge badge-success"><?php echo $addition['quantity']; ?></span></td>
                                        <?php endif; ?>
                                        <td>
                                            <?php echo !empty($addition['unit_cost']) ? '₱' . number_format($addition['unit_cost'], 2) : '-'; ?>
                                        </td>
                                        <td>
                                            <strong>
                                            <?php
                                            if ($report_type == 'fixed') {
                                                $total = !empty($addition['unit_cost']) ? $addition['unit_cost'] : 0;
                                            } else {
                                                $total = !empty($addition['unit_cost']) ? $addition['quantity'] * $addition['unit_cost'] : 0;
                                            }
                                            echo !empty($total) ? '₱' . number_format($total, 2) : '-';
                                            ?>
                                            </strong>
                                        </td>
                                        <td><?php echo $addition['reference_document'] ?? '-'; ?></td>
                                        <td>
                                            <?php
                                            if ($report_type == 'fixed') {
                                                echo $addition['created_by_name'] ?? '-';
                                            } else {
                                                echo $addition['performed_by_name'] ?? '-';
                                            }
                                            ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div>
                        <select id="pageSize" class="form-select form-select-sm">
                            <option value="10">10 per page</option>
                            <option value="25">25 per page</option>
                            <option value="50">50 per page</option>
                            <option value="100">100 per page</option>
                        </select>
                    </div>
                    <nav aria-label="Table navigation">
                        <ul class="pagination pagination-sm justify-content-end mb-0">
                            <li class="page-item disabled">
                                <a class="page-link" href="#" tabindex="-1">Previous</a>
                            </li>
                            <li class="page-item active"><a class="page-link" href="#">1</a></li>
                            <li class="page-item"><a class="page-link" href="#">2</a></li>
                            <li class="page-item"><a class="page-link" href="#">3</a></li>
                            <li class="page-item">
                                <a class="page-link" href="#">Next</a>
                            </li>
                        </ul>
                    </nav>
                </div>
            <?php else: ?>
                <div class="alert alert-info">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-info-circle fa-2x me-3"></i>
                        <div>
                            <h5 class="alert-heading mb-1">No Data Found</h5>
                            <p class="mb-0">No <?php echo $report_type == 'consumable' ? 'inventory' : 'asset'; ?> additions found for the selected period. Try adjusting your filters.</p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Category Breakdown Chart -->
    <?php if (count($items_by_category) > 0): ?>
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-chart-pie me-2"></i>Additions by Category
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="chart-container">
                        <canvas id="categoryChart" width="400" height="300"></canvas>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Category</th>
                                    <th>Additions</th>
                                    <th>Total Items</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($items_by_category as $cat_id => $data): ?>
                                    <tr>
                                        <td><?php echo $data['name']; ?></td>
                                        <td><?php echo $data['count']; ?></td>
                                        <td><?php echo $data['quantity']; ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- JavaScript for the report -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<!-- Additional print styles -->
<style>
@media print {
  /* Hide all elements by default */
  body > *:not(#printSection) {
    display: none !important;
  }

  /* Show only the print section */
  #printSection {
    display: block !important;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    background-color: white !important;
    color: black !important;
    padding: 15px !important;
  }

  /* Hide footer path and links */
  footer, .footer, .footer-content, .footer-links, .copyright {
    display: none !important;
    visibility: hidden !important;
  }

  /* Ensure all content in print section is visible */
  #printSection * {
    display: block;
    visibility: visible !important;
    color: black !important;
  }

  /* Table styling for print */
  #printSection table {
    width: 100% !important;
    border-collapse: collapse !important;
    margin-bottom: 20px !important;
  }

  #printSection table th {
    background-color: #f2f2f2 !important;
    -webkit-print-color-adjust: exact !important;
    color-adjust: exact !important;
    border-bottom: 1px solid #ddd !important;
    padding: 8px !important;
    text-align: left !important;
    font-weight: bold !important;
  }

  #printSection table td {
    padding: 8px !important;
    border-bottom: 1px solid #eee !important;
    text-align: left !important;
  }

  /* Fix for table display */
  #printSection table {
    display: table !important;
  }

  #printSection tr {
    display: table-row !important;
  }

  #printSection th,
  #printSection td {
    display: table-cell !important;
  }

  #printSection thead {
    display: table-header-group !important;
  }

  #printSection tbody {
    display: table-row-group !important;
  }

  /* Remove backgrounds and borders */
  body, .card, .card-body {
    background: white !important;
    border: none !important;
    box-shadow: none !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  /* Print header styling */
  .print-header-container {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 20px !important;
  }

  .logo-container {
    text-align: left !important;
  }

  .header-center {
    text-align: center !important;
    flex-grow: 1 !important;
  }

  .print-info {
    text-align: right !important;
  }

  .report-summary {
    text-align: center !important;
    margin-bottom: 20px !important;
  }

  /* Table formatting */
  .table {
    border-collapse: collapse;
    width: 100%;
    margin-top: 20px !important;
  }

  .table th {
    background-color: #f9f9f9 !important;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
    border-bottom: 1px solid #ddd !important;
    padding: 8px !important;
  }

  .table td {
    padding: 8px !important;
    border-bottom: 1px solid #eee !important;
  }

  /* Move to landscape for wider tables */
  @page {
    size: landscape;
    margin: 1cm;
  }

  /* Remove any footer content added by the browser */
  @page {
    margin-bottom: 1cm;
    @bottom-center {
      content: "";
    }
    @bottom-left {
      content: "";
    }
    @bottom-right {
      content: "";
    }
  }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle date range inputs based on time period selection
    const timePeriodSelect = document.getElementById('time_period');
    const customDateRange = document.getElementById('customDateRange');
    const specificDateRange = document.getElementById('specificDateRange');
    const startDateInput = document.getElementById('start_date');
    const endDateInput = document.getElementById('end_date');
    const specificDateInput = document.getElementById('specific_date');

    timePeriodSelect.addEventListener('change', function() {
        // Hide all date inputs first
        customDateRange.style.display = 'none';
        specificDateRange.style.display = 'none';

        // Show the appropriate date input based on selection
        if (this.value === 'custom') {
            customDateRange.style.display = 'flex';
        } else if (this.value === 'specific_date') {
            specificDateRange.style.display = 'block';
        }
    });

    // Handle form submission
    document.querySelector('form').addEventListener('submit', function(e) {
        // Disable unused date inputs based on time period selection
        if (timePeriodSelect.value !== 'custom') {
            const customStartInput = document.querySelector('input[name="custom_start_date"]');
            const customEndInput = document.querySelector('input[name="custom_end_date"]');

            if (customStartInput) customStartInput.disabled = true;
            if (customEndInput) customEndInput.disabled = true;
        }

        if (timePeriodSelect.value !== 'specific_date') {
            const specificInput = document.querySelector('input[name="specific_date"]');
            if (specificInput) specificInput.disabled = true;
        }
    });

    // Table search functionality
    const tableSearch = document.getElementById('tableSearch');
    if (tableSearch) {
        tableSearch.addEventListener('keyup', function() {
            const searchTerm = this.value.toLowerCase();
            const table = document.getElementById('additionsTable');
            if (!table) return;

            const rows = table.querySelectorAll('tbody tr');

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    }

    // Page size selector
    const pageSize = document.getElementById('pageSize');
    if (pageSize) {
        pageSize.addEventListener('change', function() {
            const size = parseInt(this.value);
            const table = document.getElementById('additionsTable');
            if (!table) return;

            const rows = table.querySelectorAll('tbody tr');

            rows.forEach((row, index) => {
                if (index < size) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });

            // Update pagination
            updatePagination(1, Math.ceil(rows.length / size));
        });

        // Trigger change to initialize
        pageSize.dispatchEvent(new Event('change'));
    }

    // Pagination functionality
    function updatePagination(currentPage, totalPages) {
        const pagination = document.querySelector('.pagination');
        if (!pagination) return;

        // Clear existing page items except first and last
        const pageItems = pagination.querySelectorAll('.page-item:not(:first-child):not(:last-child)');
        pageItems.forEach(item => item.remove());

        // Add page items
        const lastItem = pagination.querySelector('.page-item:last-child');

        for (let i = totalPages; i >= 1; i--) {
            const li = document.createElement('li');
            li.className = `page-item ${i === currentPage ? 'active' : ''}`;

            const a = document.createElement('a');
            a.className = 'page-link';
            a.href = '#';
            a.textContent = i;

            a.addEventListener('click', function(e) {
                e.preventDefault();
                goToPage(i);
            });

            li.appendChild(a);
            pagination.insertBefore(li, lastItem);
        }

        // Update previous/next buttons
        const prevButton = pagination.querySelector('.page-item:first-child');
        const nextButton = pagination.querySelector('.page-item:last-child');

        if (currentPage === 1) {
            prevButton.classList.add('disabled');
        } else {
            prevButton.classList.remove('disabled');
        }

        if (currentPage === totalPages) {
            nextButton.classList.add('disabled');
        } else {
            nextButton.classList.remove('disabled');
        }
    }

    function goToPage(page) {
        const pageSize = parseInt(document.getElementById('pageSize').value);
        const table = document.getElementById('additionsTable');
        if (!table) return;

        const rows = table.querySelectorAll('tbody tr');
        const totalPages = Math.ceil(rows.length / pageSize);

        rows.forEach((row, index) => {
            if (index >= (page - 1) * pageSize && index < page * pageSize) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });

        // Update pagination
        updatePagination(page, totalPages);
    }

    // Add click handlers to pagination buttons
    document.querySelector('.pagination .page-item:first-child a').addEventListener('click', function(e) {
        e.preventDefault();
        const activePage = document.querySelector('.pagination .page-item.active');
        if (activePage && activePage.previousElementSibling && activePage.previousElementSibling.classList.contains('page-item')) {
            activePage.previousElementSibling.querySelector('a').click();
        }
    });

    document.querySelector('.pagination .page-item:last-child a').addEventListener('click', function(e) {
        e.preventDefault();
        const activePage = document.querySelector('.pagination .page-item.active');
        if (activePage && activePage.nextElementSibling && activePage.nextElementSibling.classList.contains('page-item')) {
            activePage.nextElementSibling.querySelector('a').click();
        }
    });

    // Make table rows clickable
    const tableRows = document.querySelectorAll('#additionsTable tbody tr');
    tableRows.forEach(row => {
        row.style.cursor = 'pointer';
        row.addEventListener('click', function() {
            // Toggle highlight class
            this.classList.toggle('table-active');
        });
    });

    // Excel Export functionality
    const exportExcelBtn = document.getElementById('exportExcelBtn');
    if (exportExcelBtn) {
        exportExcelBtn.addEventListener('click', function() {
            const table = document.getElementById('additionsTable');
            if (!table) return;

            // Load the SheetJS library if not already loaded
            if (typeof XLSX === 'undefined') {
                const script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js';
                script.onload = function() {
                    exportToExcel(table);
                };
                document.head.appendChild(script);
            } else {
                exportToExcel(table);
            }
        });
    }

    function exportToExcel(table) {
        // Create workbook and worksheet
        const wb = XLSX.utils.book_new();

        // Get headers
        const headers = [];
        table.querySelectorAll('thead th').forEach(th => {
            headers.push(th.textContent.trim());
        });

        // Get rows
        const rows = [headers];
        table.querySelectorAll('tbody tr').forEach(tr => {
            if (tr.style.display !== 'none') { // Only include visible rows
                const row = [];
                tr.querySelectorAll('td').forEach(td => {
                    // Get text content without extra spaces
                    row.push(td.textContent.trim().replace(/\s+/g, ' '));
                });
                rows.push(row);
            }
        });

        // Create worksheet
        const ws = XLSX.utils.aoa_to_sheet(rows);

        // Add worksheet to workbook
        XLSX.utils.book_append_sheet(wb, ws, "Additions Report");

        // Generate Excel file and trigger download
        XLSX.writeFile(wb, '<?php echo $report_type; ?>_additions_report_<?php echo date('Y-m-d'); ?>.xlsx');
    }

    // PDF Export functionality
    const exportPdfBtn = document.getElementById('exportPdfBtn');
    if (exportPdfBtn) {
        exportPdfBtn.addEventListener('click', function() {
            const table = document.getElementById('additionsTable');
            if (!table) return;

            // Load the jsPDF and html2canvas libraries if not already loaded
            if (typeof jspdf === 'undefined' || typeof html2canvas === 'undefined') {
                // Load html2canvas first
                const html2canvasScript = document.createElement('script');
                html2canvasScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js';
                document.head.appendChild(html2canvasScript);

                // Then load jsPDF
                const jspdfScript = document.createElement('script');
                jspdfScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
                jspdfScript.onload = function() {
                    // Wait a bit to make sure both libraries are loaded
                    setTimeout(function() {
                        exportToPdf();
                    }, 500);
                };
                document.head.appendChild(jspdfScript);
            } else {
                exportToPdf();
            }
        });
    }

    function exportToPdf() {
        // Create a clone of the report content for PDF export
        const reportContent = document.getElementById('reportContent');
        const printSection = document.createElement('div');
        printSection.id = 'pdfExportSection';
        printSection.style.width = '100%';
        printSection.style.padding = '20px';
        printSection.style.backgroundColor = 'white';

        // Create the header with logo and info
        const headerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <div style="text-align: left;">
                    <img src="/choims/assets/img/prqlogo1.jpg" alt="Logo" style="height: 60px; width: auto;">
                </div>
                <div style="text-align: center; flex-grow: 1;">
                    <h2 style="margin-bottom: 5px; font-size: 18px; font-weight: bold;">
                        <?php echo $report_type == 'consumable' ? 'Consumable Inventory' : 'Fixed Asset'; ?> Additions Report
                    </h2>
                    <p style="margin: 0; font-size: 12px;">
                        <strong>Time Period:</strong> <?php echo date('M d, Y', strtotime($start_date)); ?> - <?php echo date('M d, Y', strtotime($end_date)); ?><br>
                        <strong>Total Items:</strong> <?php echo $total_items; ?> |
                        <strong>Total Value:</strong> ₱<?php echo number_format($total_value, 2); ?>
                    </p>
                </div>
                <div style="text-align: right;">
                    <p style="margin: 0; font-size: 10px;">
                        <strong>Generated by:</strong> <?php echo $_SESSION['full_name'] ?? $_SESSION['username']; ?><br>
                        <strong>Date & Time:</strong> <?php echo date('F d, Y h:i A'); ?>
                    </p>
                </div>
            </div>
        `;

        // Get the table clone
        const tableClone = document.querySelector('.table-responsive table').cloneNode(true);
        tableClone.style.width = '100%';
        tableClone.style.borderCollapse = 'collapse';

        // Style the table for PDF
        const tableRows = tableClone.querySelectorAll('tr');
        tableRows.forEach(row => {
            if (row.style.display !== 'none') { // Only include visible rows
                const cells = row.querySelectorAll('th, td');
                cells.forEach(cell => {
                    cell.style.border = '1px solid #ddd';
                    cell.style.padding = '8px';
                    cell.style.textAlign = 'left';
                });
            } else {
                row.remove(); // Remove hidden rows
            }
        });

        // Add header and table to the print section
        printSection.innerHTML = headerHTML;
        const tableWrapper = document.createElement('div');
        tableWrapper.appendChild(tableClone);
        printSection.appendChild(tableWrapper);

        // Temporarily add to document for html2canvas
        document.body.appendChild(printSection);

        // Use html2canvas to convert to image
        html2canvas(printSection, {
            scale: 1.5, // Higher scale for better quality
            useCORS: true,
            logging: false
        }).then(canvas => {
            // Remove the temporary element
            document.body.removeChild(printSection);

            // Create PDF
            const pdf = new jspdf.jsPDF('l', 'mm', 'a4'); // Landscape orientation

            // Calculate dimensions
            const imgData = canvas.toDataURL('image/png');
            const pdfWidth = pdf.internal.pageSize.getWidth();
            const pdfHeight = pdf.internal.pageSize.getHeight();
            const imgWidth = canvas.width;
            const imgHeight = canvas.height;
            const ratio = Math.min(pdfWidth / imgWidth, pdfHeight / imgHeight);
            const imgX = (pdfWidth - imgWidth * ratio) / 2;
            const imgY = 10;

            // Add image to PDF
            pdf.addImage(imgData, 'PNG', imgX, imgY, imgWidth * ratio, imgHeight * ratio);

            // Save PDF
            pdf.save('<?php echo $report_type; ?>_additions_report_<?php echo date('Y-m-d'); ?>.pdf');
        });
    }

    // Print report functionality
    document.getElementById('printReportBtn').addEventListener('click', function() {
        // Prepare the document for printing
        const originalTitle = document.title;
        document.title = "<?php echo ($report_type == 'consumable' ? 'Consumable Inventory' : 'Fixed Asset'); ?> Additions Report";

        // Store current user information for the print
        const currentUser = "<?php echo $_SESSION['full_name'] ?? $_SESSION['username']; ?>";
        const currentDateTime = "<?php echo date('F d, Y h:i A'); ?>";

        // Create a print section if it doesn't exist
        let printSection = document.getElementById('printSection');
        if (printSection) {
            printSection.remove(); // Remove existing print section if it exists
        }

        // Create a new print section
        printSection = document.createElement('div');
        printSection.id = 'printSection';
        document.body.appendChild(printSection);

        // Create the header with logo and info
        const headerHTML = `
            <table width="100%" style="margin-bottom: 20px;">
                <tr>
                    <td width="20%" style="text-align: left; vertical-align: middle;">
                        <img src="/choims/assets/img/prqlogo1.jpg" alt="Logo" style="height: 80px; width: auto;">
                    </td>
                    <td width="60%" style="text-align: center; vertical-align: middle;">
                        <h2 style="margin-bottom: 5px; font-size: 20px; font-weight: bold;">
                            <?php echo $report_type == 'consumable' ? 'Consumable Inventory' : 'Fixed Asset'; ?> Additions Report
                        </h2>
                        <p style="margin: 0; font-size: 14px;">
                            <strong>Time Period:</strong> <?php echo date('M d, Y', strtotime($start_date)); ?> - <?php echo date('M d, Y', strtotime($end_date)); ?><br>
                            <strong>Total Items Added:</strong> <?php echo $total_items; ?> |
                            <strong>Total Value:</strong> ₱<?php echo number_format($total_value, 2); ?>
                            <?php if (!empty($location_id)): ?>
                             | <strong>Location:</strong> <?php
                                mysqli_data_seek($locationsResult, 0);
                                while ($location = mysqli_fetch_assoc($locationsResult)) {
                                    if ($location['location_id'] == $location_id) {
                                        echo $location['location_name'];
                                        break;
                                    }
                                }
                            ?>
                            <?php endif; ?>
                            <?php if (!empty($category_id)): ?>
                             | <strong>Category:</strong> <?php
                                mysqli_data_seek($categoriesResult, 0);
                                while ($category = mysqli_fetch_assoc($categoriesResult)) {
                                    if ($category['category_id'] == $category_id) {
                                        echo $category['category_name'];
                                        break;
                                    }
                                }
                            ?>
                            <?php endif; ?>
                        </p>
                    </td>
                    <td width="20%" style="text-align: right; vertical-align: middle;">
                        <p style="margin: 0; font-size: 12px;">
                            <strong>Printed by:</strong> ${currentUser}<br>
                            <strong>Date & Time:</strong> ${currentDateTime}
                        </p>
                    </td>
                </tr>
            </table>
        `;

        // Get the table content - clone it to ensure we get a complete copy
        const tableClone = document.querySelector('.table-responsive table').cloneNode(true);

        // Create a wrapper for the table
        const tableWrapper = document.createElement('div');
        tableWrapper.className = 'table-wrapper';
        tableWrapper.appendChild(tableClone);

        // Combine everything into the print section
        printSection.innerHTML = headerHTML;
        printSection.appendChild(tableWrapper);

        // Add a small delay to allow the content to render
        setTimeout(function() {
            // Hide any footer elements that might be present
            const footerElements = document.querySelectorAll('footer, .footer, .footer-content, .copyright');
            footerElements.forEach(el => {
                if (el) {
                    el.style.display = 'none';
                    el.style.visibility = 'hidden';
                }
            });

            // Trigger the print dialog
            window.print();

            // Reset the title after printing
            setTimeout(function() {
                document.title = originalTitle;
                // Remove the print section after printing
                if (printSection) {
                    printSection.remove();
                }

                // Restore footer elements
                footerElements.forEach(el => {
                    if (el) {
                        el.style.display = '';
                        el.style.visibility = '';
                    }
                });
            }, 100);
        }, 100);
    });

    <?php if (count($items_by_category) > 0): ?>
    // Create chart
    const categoryData = {
        labels: [<?php echo implode(', ', array_map(function($data) { return "'" . $data['name'] . "'"; }, $items_by_category)); ?>],
        datasets: [{
            label: 'Items Added by Category',
            data: [<?php echo implode(', ', array_map(function($data) { return $data['quantity']; }, $items_by_category)); ?>],
            backgroundColor: [
                'rgba(75, 192, 192, 0.7)',
                'rgba(54, 162, 235, 0.7)',
                'rgba(255, 205, 86, 0.7)',
                'rgba(255, 99, 132, 0.7)',
                'rgba(153, 102, 255, 0.7)',
                'rgba(255, 159, 64, 0.7)',
                'rgba(201, 203, 207, 0.7)'
            ],
            borderColor: [
                'rgb(75, 192, 192)',
                'rgb(54, 162, 235)',
                'rgb(255, 205, 86)',
                'rgb(255, 99, 132)',
                'rgb(153, 102, 255)',
                'rgb(255, 159, 64)',
                'rgb(201, 203, 207)'
            ],
            borderWidth: 2
        }]
    };

    const ctx = document.getElementById('categoryChart').getContext('2d');
    const categoryChart = new Chart(ctx, {
        type: 'pie',
        data: categoryData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right',
                    labels: {
                        font: {
                            size: 12
                        },
                        padding: 20
                    }
                },
                title: {
                    display: true,
                    text: 'Items Added by Category',
                    font: {
                        size: 16,
                        weight: 'bold'
                    }
                }
            }
        }
    });
    <?php endif; ?>
});
</script>

<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php');
?>